export interface GameEventHandlers {
  /** Called when the player's score changes */
  onScoreChanged?: (score: number) => void;
  
  /** Called when the player loses a life (hits spike, goes out of bounds, etc.) */
  onLoseLife?: () => void;
  
  /** Called when the player collects an item/collectible */
  onCollectibleCollected?: (collectibleType?: string) => void;
  
  /** Called when the game state needs to be saved */
  onGameStateChanged?: (gameState: any) => void;
}
