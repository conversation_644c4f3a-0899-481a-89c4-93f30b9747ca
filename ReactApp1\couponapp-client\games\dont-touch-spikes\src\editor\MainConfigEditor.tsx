import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription, CardContent } from '@repo/shared/components/ui/card'
import { Input } from '@repo/shared/components/ui/input'
import { Label } from '@repo/shared/components/ui/label'
import { GameEditorComponentProps } from '@repo/shared/lib/game/game'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Button } from '@repo/shared/components/ui/button'
import { RefreshCw } from 'lucide-react'
import { ReactGameConfig, defaultGameConfig } from '../types/config'
import { SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { Slider } from '@repo/shared/components/ui/slider'

// Sound Asset Picker component
interface SoundAssetPickerProps {
    label: string
    assetUrl: SoundAssetUrl
    onSelect: (assetId: string | null) => void
    onToggle: (enabled: boolean) => void
    onVolumeChange?: (volume: number) => void
    extensions?: string[]
}

function SoundAssetPicker({ label, assetUrl, onSelect, onToggle, onVolumeChange, extensions = ['mp3', 'wav', 'ogg'] }: SoundAssetPickerProps) {
    const enabled = assetUrl?.enabled !== false
    const volume = assetUrl?.volume !== undefined ? assetUrl.volume : 0.5

    return (
        <div className="space-y-3 p-1">
            <div className="flex justify-between items-center">
                <Label className="font-medium">{label}</Label>
                <div className="flex items-center gap-2">
                    <Label htmlFor={`sound-toggle-${label}`} className="text-sm text-muted-foreground cursor-pointer">
                        {enabled ? 'Enabled' : 'Disabled'}
                    </Label>
                    <Switch id={`sound-toggle-${label}`} checked={enabled} onCheckedChange={onToggle} />
                </div>
            </div>
            {enabled ? (
                <>
                    <AssetPicker onSelect={onSelect} assetUrl={assetUrl} extensions={extensions} />

                    <div className="mt-3">
                        <div className="flex justify-between items-center mb-1">
                            <Label className="text-sm">Volume</Label>
                            <span className="text-xs text-muted-foreground">{Math.round(volume * 100)}%</span>
                        </div>
                        <Slider
                            value={[volume * 100]}
                            min={0}
                            max={100}
                            step={1}
                            onValueChange={(values) => onVolumeChange && onVolumeChange(values[0] / 100)}
                        />
                    </div>
                </>
            ) : (
                <div className="p-3 bg-muted/50 rounded-md border border-dashed border-muted-foreground/40 text-center">
                    <p className="text-sm text-muted-foreground">Sound disabled. Enable to select an audio file.</p>
                </div>
            )}
        </div>
    )
}

export default function MainConfigEditor({ config, updateConfig }: GameEditorComponentProps) {
    if (!config) config = {}
    // if (!config.gameEndHandler) config.gameEndHandler = {}

    const handleConfigChange = (key: keyof ReactGameConfig, value: any) => {
        updateConfig({ ...config, [key]: value })
    }

    const handleGameEndHandlerChange = (updates: any) => {
        handleConfigChange('gameEndHandler', {
            ...config.gameEndHandler,
            ...updates,
        })
    }

    const handleGameRewardsHandlerChange = (updates: any) => {
        handleConfigChange('gameRewardsHandler', {
            ...config.gameRewardsHandler,
            ...updates,
        })
    }

    const handleResetToDefault = (key: keyof ReactGameConfig) => {
        const defaultValue = defaultGameConfig[key]
        if (defaultValue !== undefined) {
            handleConfigChange(key, defaultValue)
        }
    }

    // Sound handlers
    const handleSoundAssetChange = (soundKey: keyof ReactGameConfig, assetId: string | null) => {
        const currentAsset = (config[soundKey] as SoundAssetUrl) || { enabled: true }
        handleConfigChange(soundKey, assetId != null ? { ...currentAsset, assetId } : null)
    }

    const handleSoundToggle = (soundKey: keyof ReactGameConfig, enabled: boolean) => {
        const currentAsset = (config[soundKey] as SoundAssetUrl) || {}
        handleConfigChange(soundKey, { ...currentAsset, enabled })
    }

    const handleSoundVolumeChange = (soundKey: keyof ReactGameConfig, volume: number) => {
        const currentAsset = (config[soundKey] as SoundAssetUrl) || { enabled: true }
        handleConfigChange(soundKey, { ...currentAsset, volume })
    }

    return (
        <div className="space-y-6">
            {/* Main Background Card */}
            <Card>
                <CardHeader>
                    <CardTitle>Main Background</CardTitle>
                    <CardDescription>Configure the main game background</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex justify-end mb-2">
                        <Button variant="outline" size="sm" onClick={() => handleResetToDefault('mainBackground')} className="flex items-center gap-1">
                            <RefreshCw size={14} />
                            Reset
                        </Button>
                    </div>

                    <div>
                        <Label className="mb-2 block">Background Image</Label>
                        <AssetPicker
                            onSelect={(assetId) =>
                                handleConfigChange('mainBackground', {
                                    ...config.mainBackground,
                                    asset: { assetId },
                                })
                            }
                            assetUrl={config.mainBackground?.asset}
                            extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg', 'gif']}
                        />
                    </div>

                    <div>
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Background Color</Label>
                            <Switch
                                checked={config.mainBackground?.useBackgroundColor !== false}
                                onCheckedChange={(useBackgroundColor) =>
                                    handleConfigChange('mainBackground', {
                                        ...config.mainBackground,
                                        useBackgroundColor,
                                    })
                                }
                            />
                        </div>
                        <div className={`transition-opacity ${config.mainBackground?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                            <ColorPicker
                                color={config.mainBackground?.fill || '#faf8ef'}
                                onChange={(backgroundColor) =>
                                    handleConfigChange('mainBackground', {
                                        ...config.mainBackground,
                                        fill: backgroundColor,
                                    })
                                }
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Lives Handler Card */}
            <Card>
                <CardHeader>
                    <CardTitle>Lives Handler</CardTitle>
                    <CardDescription>Configure the lives system for the game</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex justify-end mb-2">
                        <Button variant="outline" size="sm" onClick={() => handleResetToDefault('gameEndHandler')} className="flex items-center gap-1">
                            <RefreshCw size={14} />
                            Reset
                        </Button>
                    </div>

                    <div className="space-y-4">
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Use Lives System</Label>
                            <Switch checked={config.gameEndHandler?.useLives || false} onCheckedChange={(value) => handleGameEndHandlerChange({ useLives: value })} />
                        </div>

                        {config.gameEndHandler?.useLives && (
                            <div className="space-y-2">
                                <Label htmlFor="lives-count">Number of Lives</Label>
                                <Input
                                    id="lives-count"
                                    type="number"
                                    value={config.gameEndHandler?.livesCount || 3}
                                    onChange={(e) =>
                                        handleGameEndHandlerChange({
                                            livesCount: Number(e.target.value),
                                        })
                                    }
                                    min={1}
                                    max={10}
                                />
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Rewards Handler Card */}
            <Card>
                <CardHeader>
                    <CardTitle>Rewards Settings</CardTitle>
                    <CardDescription>Configure the rewards system for the game</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex justify-end mb-2">
                        <Button variant="outline" size="sm" onClick={() => handleResetToDefault('gameRewardsHandler')} className="flex items-center gap-1">
                            <RefreshCw size={14} />
                            Reset
                        </Button>
                    </div>

                    <div className="space-y-4">
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Enable Rewards</Label>
                            <Switch checked={config.gameRewardsHandler?.rewardsEnabled || false} onCheckedChange={(value) => handleGameRewardsHandlerChange({ rewardsEnabled: value })} />
                        </div>


                    </div>
                </CardContent>
            </Card>

            {/* Sound Settings Card */}
            <Card>
                <CardHeader>
                    <CardTitle>Sound Settings</CardTitle>
                    <CardDescription>Configure the game's sound effects and music</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex justify-end mb-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                                handleResetToDefault('backgroundMusic')
                                handleResetToDefault('winSound')
                                handleResetToDefault('gameOverSound')
                            }}
                            className="flex items-center gap-1"
                        >
                            <RefreshCw size={14} />
                            Reset All Sounds
                        </Button>
                    </div>

                    <SoundAssetPicker
                        label="Background Music"
                        assetUrl={(config.backgroundMusic as SoundAssetUrl) || defaultGameConfig.backgroundMusic}
                        onSelect={(assetId) => handleSoundAssetChange('backgroundMusic', assetId)}
                        onToggle={(enabled) => handleSoundToggle('backgroundMusic', enabled)}
                        onVolumeChange={(volume) => handleSoundVolumeChange('backgroundMusic', volume)}
                    />

                    <SoundAssetPicker
                        label="Win Sound"
                        assetUrl={(config.winSound as SoundAssetUrl) || defaultGameConfig.winSound}
                        onSelect={(assetId) => handleSoundAssetChange('winSound', assetId)}
                        onToggle={(enabled) => handleSoundToggle('winSound', enabled)}
                        onVolumeChange={(volume) => handleSoundVolumeChange('winSound', volume)}
                    />

                    <SoundAssetPicker
                        label="Game Over Sound"
                        assetUrl={(config.gameOverSound as SoundAssetUrl) || defaultGameConfig.gameOverSound}
                        onSelect={(assetId) => handleSoundAssetChange('gameOverSound', assetId)}
                        onToggle={(enabled) => handleSoundToggle('gameOverSound', enabled)}
                        onVolumeChange={(volume) => handleSoundVolumeChange('gameOverSound', volume)}
                    />
                </CardContent>
            </Card>
        </div>
    )
}
