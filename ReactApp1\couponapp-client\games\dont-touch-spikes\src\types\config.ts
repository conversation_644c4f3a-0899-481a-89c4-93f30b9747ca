import { GameEndH<PERSON><PERSON>, GameRewardsHandler, BackgroundStyle, GameTextSettings, GameButtonStyle, ContainerStyle, RewardComponentStyle, GameSoundSwitchStyle, TextStyle, AnimableSprite, CounterElementStyle } from '@repo/shared-game-utils/types/uiStyles'
import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'


export class ReactGameConfig extends GameConfig {
    @gameConfigKey({ name: '<PERSON> Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: 'Rewards Handler', configEditorType: 'rewards-handler' })
    gameRewardsHandler?: GameRewardsHandler

    @gameConfigKey({ name: 'Background Music', configEditorType: 'audio' })
    backgroundMusic?: SoundAssetUrl

    @gameConfigKey({ name: 'Win Sound', configEditorType: 'audio' })
    winSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Picking Reward Sound', configEditorType: 'audio' })
    pickingRewardSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Game Over Sound', configEditorType: 'audio' })
    gameOverSound?: SoundAssetUrl

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'background',
        width: 800,
        height: 600,
    })
    mainBackground: BackgroundStyle

    @gameConfigKey({
        name: 'Score Style',
        configEditorType: 'text-style',
    })
    scoreStyle: TextStyle

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({ name: 'Lose Life Overlay', configEditorType: 'container' })
    loseLifeOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Lose Life Title', configEditorType: 'text' })
    loseLifeTitle?: GameTextSettings

    @gameConfigKey({ name: 'Continue Button', configEditorType: 'game-button' })
    continueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Try Again Lives Display', configEditorType: 'counter' })
    tryAgainLivesStyle?: CounterElementStyle

    @gameConfigKey({ name: 'Try Again Secondary Text', configEditorType: 'text' })
    tryAgainSecondaryText?: GameTextSettings

    @gameConfigKey({ name: 'Try Again Current Score', configEditorType: 'counter' })
    tryAgainCurrentScoreStyle?: CounterElementStyle

    @gameConfigKey({ name: 'Try Again Best Score', configEditorType: 'counter' })
    tryAgainBestScoreStyle?: CounterElementStyle

    @gameConfigKey({ name: 'Game Over Overlay', configEditorType: 'container' })
    gameOverOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Game Over Title', configEditorType: 'text' })
    gameOverTitle?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Message', configEditorType: 'text' })
    gameOverMessage?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Continue Button', configEditorType: 'game-button' })
    gameOverContinueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Overlay', configEditorType: 'container' })
    rewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Reward Title', configEditorType: 'text' })
    rewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Reward Claim Button', configEditorType: 'game-button' })
    rewardClaimButton?: GameButtonStyle

    @gameConfigKey({ name: 'Out of Lives Overlay', configEditorType: 'container' })
    outOfLivesOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Out of Lives Title', configEditorType: 'text' })
    outOfLivesTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Overlay', configEditorType: 'container' })
    claimRewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Claim Reward Title', configEditorType: 'text' })
    claimRewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Button', configEditorType: 'game-button' })
    claimRewardButton?: GameButtonStyle

    @gameConfigKey({ name: 'Try Again Button', configEditorType: 'game-button' })
    tryAgainButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle

    @gameConfigKey({ name: 'Sound Switch', configEditorType: 'sound-switch', editorSettings: { toggleableVisibility: true } })
    gameSoundSwitch?: GameSoundSwitchStyle

    @gameConfigKey({ name: 'Game Area Background', configEditorType: 'image' })
    gameArea?: AssetUrl

    @gameConfigKey({ name: 'Player Image', configEditorType: 'animable-sprite' })
    player?: AnimableSprite

    @gameConfigKey({ name: 'Collectible 1 (Yellow)', configEditorType: 'animable-sprite' })
    collectible1?: AnimableSprite

    @gameConfigKey({ name: 'Collectible 2 (Blue)', configEditorType: 'animable-sprite' })
    collectible2?: AnimableSprite

    @gameConfigKey({ name: 'Collectible 3 (Purple)', configEditorType: 'animable-sprite' })
    collectible3?: AnimableSprite

    @gameConfigKey({ name: 'Spike Style', configEditorType: 'background' })
    spike?: BackgroundStyle

    @gameConfigKey({ name: 'Ground Style', configEditorType: 'background' })
    ground?: BackgroundStyle

    @gameConfigKey({ name: 'Jump Sound', configEditorType: 'audio' })
    jumpSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Spike Hit Sound', configEditorType: 'audio' })
    spikeHitSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Correct Wall Hit Sound', configEditorType: 'audio' })
    correctWallHitSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Collect Item Sound', configEditorType: 'audio' })
    collectItemSound?: SoundAssetUrl

    @gameConfigKey({
        name: 'Instruction Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    instructionText: GameTextSettings

    @gameConfigKey({ name: 'Start Screen Overlay', configEditorType: 'container' })
    startScreenOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Start Screen Title', configEditorType: 'text' })
    startScreenTitle?: GameTextSettings

    @gameConfigKey({ name: 'Start Screen Start Button', configEditorType: 'game-button' })
    startScreenStartButton?: GameButtonStyle
}

export const defaultGameConfig: ReactGameConfig = {
    gameEndHandler: {
        useLives: false,
        livesCount: 3,
    },

    gameRewardsHandler: {
        rewardsEnabled: false,
    },

    backgroundMusic: {
        enabled: true,
        absoluteUrl: 'https://example.com/background-music.mp3',
    },

    winSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/win-sound.mp3',
    },

    pickingRewardSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/picking-reward-sound.mp3',
    },

    gameOverSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/game-over-sound.mp3',
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    scoreStyle: {
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 180,
        fill: '#776e65',
        isVisible: true,
        fontWeight: '500',
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    continueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    loseLifeOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    loseLifeTitle: {
        text: 'You Lost a Life',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    gameOverTitle: {
        text: 'Game Over',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    rewardTitle: {
        text: 'You Earned a Reward!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    outOfLivesTitle: {
        text: 'Out of Lives',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardTitle: {
        text: 'Claim Your Reward or Try Again',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    claimRewardButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    tryAgainButton: {
        textConfig: {
            text: 'Try Again',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    tryAgainLivesStyle: {
        textConfig: {
            text: 'Lives: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 20,
                fill: '#776e65',
                isVisible: true,
            },
        },
        isVisible: true,
    },

    tryAgainSecondaryText: {
        text: 'Don\'t give up! Try again and beat your best score.',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 16,
            fill: '#776e65',
            isVisible: true,
            textAlign: 'center',
        },
    },

    tryAgainCurrentScoreStyle: {
        textConfig: {
            text: 'Score: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 20,
                fill: '#776e65',
                isVisible: true,
            },
        },
        isVisible: true,
    },

    tryAgainBestScoreStyle: {
        textConfig: {
            text: 'Best: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 20,
                fill: '#776e65',
                isVisible: true,
            },
        },
        isVisible: true,
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#776e65',
        titleTextAlign: 'left',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#776e65',
        descriptionTextAlign: 'left',
        padding: 16,
        maxWidth: 400,
        layout: 'horizontal-left',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    gameSoundSwitch: {
        onAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=ON',
        },
        offAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=OFF',
        },
        width: 48,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        alignment: 'right',
        isVisible: true,
    },

    gameArea: {
        absoluteUrl: 'https://placehold.co/517x988',
    },


    player: {
        asset: {
            absoluteUrl: 'https://raw.githubusercontent.com/pekseneren/unity-dont-touch-the-spikes-clone/refs/heads/master/Assets/sprites/birdFly.png',
        },
        isAnimated: true,
        animationData: {
            animationType: 'frames',
            framesCount: 2,
            frameDelay: 166, // ~6 FPS (1000ms / 6 = 166ms)
        },
    },

    collectible1: {
        asset: {
            absoluteUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f0/Eo_circle_yellow.svg/2048px-Eo_circle_yellow.svg.png',
        },
        isAnimated: false,
        animationData: {
            animationType: 'frames',
            framesCount: 1,
            frameDelay: 1000,
        },
    },

    collectible2: {
        asset: {
            absoluteUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Eo_circle_blue.svg/2048px-Eo_circle_blue.svg.png',
        },
        isAnimated: false,
        animationData: {
            animationType: 'frames',
            framesCount: 1,
            frameDelay: 1000,
        },
    },

    collectible3: {
        asset: {
            absoluteUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cc/Eo_circle_purple.svg/2048px-Eo_circle_purple.svg.png',
        },
        isAnimated: false,
        animationData: {
            animationType: 'frames',
            framesCount: 1,
            frameDelay: 1000,
        },
    },

    spike: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    ground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    jumpSound: {
        enabled: true,
        absoluteUrl: 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/6a6dce1a-b027-4f8c-8abe-a04879f9e60c.ogg',
    },

    spikeHitSound: {
        enabled: true,
        absoluteUrl: 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/b84e644e-26a8-48ef-ae6e-b03d396653ed.ogg',
    },

    correctWallHitSound: {
        enabled: true,
        absoluteUrl: 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/ae7403f9-1c78-46ac-af71-90bf969c2a86.ogg',
    },
    
    collectItemSound: {
        enabled: true,
        absoluteUrl: 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/5a3c7d15-0421-4077-9ba5-0d7178e83746.ogg',
    },

    instructionText: {
        text: 'Click or press space to jump',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 38,
            fill: '#776e65',
            isVisible: true,
        },
    },

    startScreenOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    startScreenTitle: {
        text: 'Don\'t Touch the Spikes!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 32,
            fill: '#776e65',
            isVisible: true,
            textAlign: 'center',
        },
    },

    startScreenStartButton: {
        textConfig: {
            text: 'Start Game',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 20,
                fill: '#ffffff',
                isVisible: true,
                textAlign: 'center',
            },
        },
        fill: '#8f7a66',
        width: 180,
        height: 50,
        useBackgroundColor: true,
        borderRadius: 8,
    },
}
